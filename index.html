<!-- auth.html -->
<!DOCTYPE html>
<html>
<head>
  <title>Login / Sign Up</title>
  <script src="https://www.gstatic.com/firebasejs/10.5.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.5.0/firebase-auth.js"></script>
</head>
<body>
  <h2>Login</h2>
  <input type="email" id="login-email" placeholder="Email" />
  <input type="password" id="login-password" placeholder="Password" />
  <button onclick="login()">Login</button>

  <h2>Sign Up</h2>
  <input type="email" id="signup-email" placeholder="Email" />
  <input type="password" id="signup-password" placeholder="Password" />
  <button onclick="signup()">Sign Up</button>

  <script>
    const firebaseConfig = {
      apiKey: "YOUR_API_KEY",
      authDomain: "YOUR_AUTH_DOMAIN",
      projectId: "YOUR_PROJECT_ID",
    };
    firebase.initializeApp(firebaseConfig);

    const auth = firebase.auth();

    function signup() {
      const email = document.getElementById("signup-email").value;
      const password = document.getElementById("signup-password").value;
      auth.createUserWithEmailAndPassword(email, password)
        .then(user => {
          alert("User signed up!");
        })
        .catch(error => alert(error.message));
    }

    function login() {
      const email = document.getElementById("login-email").value;
      const password = document.getElementById("login-password").value;
      auth.signInWithEmailAndPassword(email, password)
        .then(user => {
          window.location.href = "dashboard.html";
        })
        .catch(error => alert(error.message));
    }
  </script>
</body>
</html>
